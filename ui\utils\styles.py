"""
样式管理器

统一管理UI组件的样式配置
"""

import ttkbootstrap as ttk


class StyleManager:
    """样式管理器"""

    @staticmethod
    def setup_table_styles():
        """配置表格样式"""
        style = ttk.Style()

        # 配置Treeview样式
        style.configure(
            'Treeview',
            font=('Microsoft YaHei', 10),
            rowheight=50,
            borderwidth=1,
            relief='solid'
        )

        # 配置表头样式
        style.configure(
            'Treeview.Heading',
            font=('Microsoft YaHei', 10, 'bold'),
            background='#002EA6',
            foreground='white',
            borderwidth=1,
            relief='solid',
            padding=[10, 8]
        )

    @staticmethod
    def setup_modern_labelframe_style():
        """配置现代化LabelFrame样式"""
        style = ttk.Style()
        
        style.configure(
            'Modern.TLabelframe',
            borderwidth=2,
            relief='solid',
            bordercolor='#e9ecef',
            background='#ffffff'
        )
        style.configure(
            'Modern.TLabelframe.Label',
            background='#ffffff',
            foreground='#495057',
            font=('Microsoft YaHei', 12, 'bold')
        )

    @staticmethod
    def setup_tab_button_styles():
        """配置选项卡按钮样式"""
        style = ttk.Style()
        
        # 现代化选项卡按钮样式
        style.configure(
            'Modern.TabButton.TButton',
            padding=[60, 12],
            font=('Microsoft YaHei', 12, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            relief='flat',
            borderwidth=0,
            focuscolor='none'
        )

    @staticmethod
    def setup_button_styles():
        """配置按钮样式"""
        style = ttk.Style()

        # 大按钮样式
        style.configure(
            'Large.TButton',
            font=('Microsoft YaHei', 10, 'bold'),
            padding=[15, 10]
        )

    @staticmethod
    def setup_all_styles():
        """设置所有样式"""
        StyleManager.setup_table_styles()
        StyleManager.setup_modern_labelframe_style()
        StyleManager.setup_tab_button_styles()
        StyleManager.setup_button_styles()

    @staticmethod
    def configure_treeview_colors(tree_widget):
        """配置Treeview的隔行变色"""
        tree_widget.tag_configure('evenrow', background='#F5FAFE')
        tree_widget.tag_configure('oddrow', background='#DCE9F7')

        # 配置状态相关的颜色
        tree_widget.tag_configure('status_pending', foreground='#6c757d')  # 灰色 - 待计算
        tree_widget.tag_configure('status_calculating', foreground='#007bff')  # 蓝色 - 计算中
        tree_widget.tag_configure('status_completed', foreground='#28a745')  # 绿色 - 完成
        tree_widget.tag_configure('status_error', foreground='#dc3545')  # 红色 - 错误
