"""
多任务页面组件

处理批量化学式的计算
"""

import ttkbootstrap as ttk
import pandas as pd
import shutil
import os
from datetime import datetime
from typing import List, Tuple, Optional
from ui.utils.styles import StyleManager
from ui.utils.dialogs import DialogUtils
from utils.constants import TABLE_COLUMNS, COLUMN_WIDTHS, NUMBER_FORMAT
from utils.file_handler import FileHandler


class MultiTaskTab:
    """多任务计算选项卡组件"""

    def __init__(self, parent, calculator):
        self.parent = parent
        self.calculator = calculator
        self.frame = ttk.Frame(parent)
        self.batch_input_data: List[Tuple[str, float, str]] = []  # (formula, mass, status)
        self.batch_calculation_results = None
        self.create_widgets()

    def create_widgets(self):
        """创建多任务页面组件"""
        # 配置网格权重
        self.frame.grid_rowconfigure(0, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.frame)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=30)
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 输入区域
        self.create_input_section(main_frame)

    def create_input_section(self, parent):
        """创建输入区域"""
        # 确保样式已配置
        StyleManager.setup_modern_labelframe_style()
        StyleManager.setup_button_styles()
        StyleManager.setup_table_styles()

        input_frame = ttk.LabelFrame(
            parent,
            text="批量计算",
            style='Modern.TLabelframe',
            padding=25
        )
        input_frame.grid(row=0, column=0, sticky="nsew")
        input_frame.grid_rowconfigure(0, weight=1)  # 表格区域占主要空间
        input_frame.grid_columnconfigure(0, weight=1)

        # 数据表格区域
        self.create_table_section(input_frame)

        # 操作按钮区域（移到底部）
        self.create_button_section(input_frame)

    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(15, 0))

        # 按钮容器 - 使用grid布局实现均匀分布
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(fill="x", expand=True)

        # 配置列权重，实现均匀分布
        for i in range(6):
            buttons_container.grid_columnconfigure(i, weight=1)

        # 创建六个按钮，按照设置页面的样式，均匀分布
        ttk.Button(
            buttons_container,
            text="添加",
            command=self.add_batch_formula_row,
            bootstyle="dark-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=0, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="清空",
            command=self.clear_batch_data,
            bootstyle="warning-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=1, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="模板",
            command=self.download_multi_template,
            bootstyle="info-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=2, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="导入",
            command=self.import_from_excel,
            bootstyle="primary-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=3, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="计算",
            command=self.calculate_and_export_batch,
            bootstyle="success-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=4, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="下载",
            command=self.download_results,
            bootstyle="secondary-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=5, sticky="ew", padx=2)

    def create_table_section(self, parent):
        """创建表格区域"""
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=0, column=0, sticky="nsew")
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 表格
        columns = TABLE_COLUMNS['multi_input']
        self.input_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=25
        )

        # 设置列
        for col in columns:
            self.input_tree.heading(col, text=col)
            width = COLUMN_WIDTHS.get(col, 100)
            anchor = "w" if col == "化学式" else "center"
            self.input_tree.column(col, width=width, anchor=anchor)

        # 滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.input_tree.yview)
        self.input_tree.configure(yscrollcommand=scrollbar.set)

        # 配置隔行变色
        StyleManager.configure_treeview_colors(self.input_tree)

        # 绑定双击编辑事件
        self.input_tree.bind('<Double-1>', self.on_tree_double_click)

        self.input_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

    def add_batch_formula_row(self):
        """添加批量计算行"""
        # 直接添加空白行供用户填写，初始状态为"待计算"
        self.batch_input_data.append(("", 0.0, "待计算"))
        self.refresh_batch_input_table()

        # 自动选中新添加的行进行编辑
        if self.batch_input_data:
            last_item = self.input_tree.get_children()[-1]
            self.input_tree.selection_set(last_item)
            self.input_tree.focus(last_item)
            # 触发编辑模式
            self.on_tree_double_click(None, item=last_item, column='#2')  # 编辑化学式列

    def import_from_excel(self):
        """从Excel导入批量数据"""
        file_path = DialogUtils.get_open_file_path("选择Excel文件")
        if not file_path:
            return

        try:
            df = pd.read_excel(file_path)

            # 检查必要的列
            required_columns = ['化学式', '总质量']
            if not all(col in df.columns for col in required_columns):
                DialogUtils.show_error(
                    "格式错误",
                    f"Excel文件必须包含以下列: {', '.join(required_columns)}"
                )
                return

            # 导入数据
            imported_count = 0
            for _, row in df.iterrows():
                formula = str(row['化学式']).strip()
                try:
                    mass = float(row['总质量'])
                    if formula and mass > 0:
                        self.batch_input_data.append((formula, mass, "待计算"))
                        imported_count += 1
                except (ValueError, TypeError):
                    continue

            self.refresh_batch_input_table()
            DialogUtils.show_info("导入成功", f"成功导入 {imported_count} 条数据")

        except Exception as e:
            DialogUtils.show_error("导入失败", f"导入Excel文件时出错: {str(e)}")

    def download_multi_template(self):
        """下载多任务模板"""
        try:
            file_path = DialogUtils.get_save_file_path("多任务模板", ".xlsx")
            if not file_path:
                return

            # 使用 FileHandler 获取正确的模板文件路径
            template_file = FileHandler.get_template_file_path()

            # 检查模板文件是否存在
            if not os.path.exists(template_file):
                DialogUtils.show_error("❌ 模板文件不存在", f"未找到模板文件:\n{template_file}")
                return

            # 直接复制模板文件到用户指定位置
            shutil.copy2(template_file, file_path)
            DialogUtils.show_info("✅ 下载成功", f"多任务模板已保存到:\n{file_path}")

        except Exception as e:
            DialogUtils.show_error("❌ 下载失败", f"下载模板时出错:\n{str(e)}")

    def clear_batch_data(self):
        """清空批量计算数据"""
        if self.batch_input_data:
            if DialogUtils.ask_yes_no("确认清空", "确定要清空所有数据吗？"):
                self.batch_input_data.clear()
                self.refresh_batch_input_table()

    def on_tree_double_click(self, event, item=None, column=None):
        """处理表格双击事件"""
        if item is None:
            item = self.input_tree.selection()[0] if self.input_tree.selection() else None
        if not item:
            return

        # 获取点击的列
        if column is None and event is not None:
            column = self.input_tree.identify_column(event.x)
        elif column is None:
            column = '#2'  # 默认编辑化学式列

        column_index = int(column.replace('#', '')) - 1

        # 获取行索引
        row_index = self.input_tree.index(item)

        # 只允许编辑化学式或质量列
        if column_index in [1, 2]:  # 化学式或质量列
            self.edit_batch_cell(item, column_index, row_index)

    def edit_batch_cell(self, item, column_index, row_index):
        """编辑批量计算表格单元格"""
        if row_index >= len(self.batch_input_data):
            return

        # 获取当前值
        current_formula, current_mass, current_status = self.batch_input_data[row_index]
        current_value = current_formula if column_index == 1 else str(current_mass)

        # 获取列名
        columns = self.input_tree['columns']
        column_name = columns[column_index]

        # 获取单元格位置
        bbox = self.input_tree.bbox(item, column_name)
        if not bbox:
            return

        # 创建编辑框
        edit_var = ttk.StringVar(value=current_value)
        edit_entry = ttk.Entry(
            self.input_tree,
            textvariable=edit_var,
            font=('Microsoft YaHei', 11)
        )

        # 定位编辑框
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])
        edit_entry.focus()
        edit_entry.select_range(0, 'end')

        def save_edit():
            new_value = edit_var.get().strip()
            if column_index == 1:  # 化学式
                if new_value:
                    # 修改化学式时，重置状态为"待计算"
                    self.batch_input_data[row_index] = (new_value, current_mass, "待计算")
                    self.refresh_batch_input_table()
            else:  # 质量
                try:
                    new_mass = float(new_value)
                    if new_mass > 0:
                        # 修改质量时，重置状态为"待计算"
                        self.batch_input_data[row_index] = (current_formula, new_mass, "待计算")
                        self.refresh_batch_input_table()
                    else:
                        DialogUtils.show_error("输入错误", "质量必须是正数")
                except ValueError:
                    DialogUtils.show_error("输入错误", "质量必须是数字")
            edit_entry.destroy()

        def cancel_edit():
            edit_entry.destroy()

        # 绑定事件
        edit_entry.bind('<Return>', lambda _: save_edit())
        edit_entry.bind('<Escape>', lambda _: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda _: save_edit())

    def download_results(self):
        """下载计算结果"""
        if not self.batch_calculation_results:
            DialogUtils.show_warning("提示", "请先执行计算")
            return

        # 选择保存路径
        file_path = DialogUtils.get_save_file_path("批量计算结果")
        if not file_path:
            return

        try:
            # 导出结果到文件
            self.export_batch_results_to_file(self.batch_calculation_results, file_path)
            DialogUtils.show_info("下载成功", f"计算结果已保存到:\n{file_path}")
        except Exception as e:
            DialogUtils.show_error("下载失败", f"保存文件时出错: {str(e)}")

    def refresh_batch_input_table(self):
        """刷新批量输入表格"""
        # 清空表格
        for item in self.input_tree.get_children():
            self.input_tree.delete(item)

        # 添加数据
        for i, (formula, mass, status) in enumerate(self.batch_input_data):
            item = self.input_tree.insert("", "end", values=[
                i + 1,
                formula,
                f"{mass:.2f}",
                status
            ])
            # 设置隔行变色
            if i % 2 == 0:
                self.input_tree.item(item, tags=('evenrow',))
            else:
                self.input_tree.item(item, tags=('oddrow',))

    def calculate_and_export_batch(self):
        """执行批量计算"""
        if not self.batch_input_data:
            DialogUtils.show_error("数据错误", "请先添加化学式数据")
            return

        try:
            # 更新所有行状态为"计算中"
            for i in range(len(self.batch_input_data)):
                formula, mass, _ = self.batch_input_data[i]
                self.batch_input_data[i] = (formula, mass, "计算中")
            self.refresh_batch_input_table()

            # 执行批量计算
            # 准备计算数据（只传递formula和mass）
            calculation_data = [(formula, mass) for formula, mass, _ in self.batch_input_data]
            results = self.calculator.calculate_multiple_formulas(calculation_data)
            self.batch_calculation_results = results

            # 更新所有行状态为"完成"
            for i in range(len(self.batch_input_data)):
                formula, mass, _ = self.batch_input_data[i]
                self.batch_input_data[i] = (formula, mass, "完成")
            self.refresh_batch_input_table()

            # 计算完成提示
            DialogUtils.show_info(
                "计算完成",
                f"已成功计算 {len(results['results'])} 个化学式配方\n\n请点击'下载'按钮保存结果"
            )

        except Exception as e:
            # 计算出错时，更新状态为"错误"
            for i in range(len(self.batch_input_data)):
                formula, mass, _ = self.batch_input_data[i]
                self.batch_input_data[i] = (formula, mass, "错误")
            self.refresh_batch_input_table()
            DialogUtils.show_error("计算错误", str(e))

    def export_batch_results_to_file(self, results, file_path):
        """将批量计算结果导出到指定文件"""
        formula_results = results['results']
        materials = results['all_materials']

        # 创建主数据表
        columns = ['原料/化学式'] + [f'配方{i+1}' for i in range(len(formula_results))] + ['总计(g)']

        data = []

        # 目标质量行
        mass_row = ['目标质量(g)']
        total_mass = sum(result['target_mass'] for result in formula_results)
        for result in formula_results:
            mass_row.append(result['target_mass'])
        mass_row.append(total_mass)
        data.append(mass_row)

        # 计算原料总量
        material_totals = {}
        for material in materials:
            material_totals[material] = sum(
                result['materials'].get(material, 0) for result in formula_results
            )

        # 原料行
        for material in sorted(materials):
            row = [material]
            for result in formula_results:
                if material in result['materials']:
                    row.append(round(result['materials'][material], NUMBER_FORMAT['mass_decimal_places']))
                else:
                    row.append(0)
            row.append(round(material_totals[material], NUMBER_FORMAT['mass_decimal_places']))
            data.append(row)

        # 创建DataFrame并保存
        df = pd.DataFrame(data, columns=columns)

        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 主计算结果
            df.to_excel(writer, sheet_name='批量计算结果', index=False)

            # 化学式信息
            formula_data = []
            for i, result in enumerate(formula_results):
                formula_data.append({
                    '配方编号': f'配方{i+1}',
                    '化学式': result['formula'],
                    '目标质量(g)': result['target_mass']
                })

            formula_df = pd.DataFrame(formula_data)
            formula_df.to_excel(writer, sheet_name='化学式信息', index=False)

    def get_frame(self):
        """获取组件框架"""
        return self.frame
